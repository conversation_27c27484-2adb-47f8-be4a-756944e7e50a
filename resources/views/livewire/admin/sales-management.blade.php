<div>
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item" aria-current="page">Sales Management</li>
                    </ul>
                </div>
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Sales Management</h2>
                        <p class="mb-0 text-muted">Manage and view all sales records with agent and branch information</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Summary Cards -->
    <div class="mb-4 row">
        <div class="col-md-2">
            <div class="text-white card bg-primary">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-white avtar avtar-s text-primary">
                            <i class="ph-duotone ph-shopping-cart"></i>
                        </div>
                        <div class="ms-2">
                            <h6 class="mb-0 text-white">Total Sales</h6>
                            <h4 class="mb-0 text-white">{{ number_format($salesSummary['total_sales']) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-white card bg-success">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-white avtar avtar-s text-success">
                            <i class="ph-duotone ph-currency-circle-dollar"></i>
                        </div>
                        <div class="ms-2">
                            <h6 class="mb-0 text-white">Total Value</h6>
                            <h4 class="mb-0 text-white">RM {{ number_format($salesSummary['total_value'], 2) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="text-white card bg-secondary">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-white avtar avtar-s text-secondary">
                            <i class="ph-duotone ph-chart-line"></i>
                        </div>
                        <div class="ms-2">
                            <h6 class="mb-0 text-white">Average Sale Value</h6>
                            <h4 class="mb-0 text-white">RM {{ number_format($salesSummary['avg_sale_value'], 2) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5>Sales Records</h5>
                            <p class="mb-0 text-muted">View and manage all sales records with agent and branch information</p>
                        </div>
                        <button class="btn btn-outline-secondary btn-sm" wire:click="toggleFilters">
                            <i class="ph-duotone ph-funnel"></i> Filters
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    @if($showFilters)
                    <div class="p-3 mb-4 rounded bg-light">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="ph-duotone ph-magnifying-glass"></i></span>
                                    <input type="text" wire:model.live="search" class="form-control" placeholder="Search sales...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Cooperative</label>
                                <select wire:model.live="selectedCooperative" class="form-select">
                                    <option value="">All Cooperatives</option>
                                    @foreach($cooperatives as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Agent</label>
                                <select wire:model.live="selectedAgent" class="form-select">
                                    <option value="">All Agents</option>
                                    @foreach($agents as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Per Page</label>
                                <select wire:model.live="perPage" class="form-select">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3 row">
                            <div class="col-12">
                                <button class="btn btn-outline-danger btn-sm" wire:click="clearFilters">
                                    <i class="ph-duotone ph-x"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Sales Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sale ID</th>
                                    <th>Product</th>
                                    <th>Agent</th>
                                    <th>Cooperative & Branch</th>
                                    <th>Price</th>
                                    <th>Date</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sales as $sale)
                                    <tr class="align-middle">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-primary">
                                                    <i class="ph-duotone ph-hash"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->{'23_senarai_jualan_id'} }}</h6>
                                                    <small class="text-muted">{{ substr($sale->id, 0, 8) }}...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-warning">
                                                    <i class="ph-duotone ph-package"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->variety_type }}</h6>
                                                    <small class="text-muted">{{ $sale->sku_number }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-info">
                                                    <i class="ph-duotone ph-user"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->agent_name }}</h6>
                                                    <small class="text-muted">{{ $sale->agent_email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-success">
                                                    <i class="ph-duotone ph-buildings"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->cooperative_name ?? 'N/A' }}</h6>
                                                    <small class="text-muted">{{ $sale->branch_name ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">RM {{ number_format($sale->total_price, 2) }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-medium">{{ \Carbon\Carbon::parse($sale->created_at)->format('M d, Y') }}</span>
                                            <small class="text-muted d-block">{{ \Carbon\Carbon::parse($sale->created_at)->format('H:i') }}</small>
                                        </td>
                                        <td class="text-center">
                                            <button class="btn btn-outline-primary btn-sm" wire:click="showSaleDetails('{{ $sale->id }}')">
                                                <i class="ph-duotone ph-eye"></i> View
                                            </button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="py-4 text-center">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="ph-duotone ph-shopping-cart text-muted" style="font-size: 3rem;"></i>
                                                <h6 class="mt-2 text-muted">No sales records found</h6>
                                                <p class="text-muted">Try adjusting your search criteria</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-3 d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                Showing {{ $sales->firstItem() ?? 0 }} to {{ $sales->lastItem() ?? 0 }} of {{ $sales->total() }} results
                            </small>
                        </div>
                        <div>
                            {{ $sales->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sale Details Modal -->
    @if($showModal && $selectedSale)
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sale Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3 text-muted">Product Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-medium">Sale ID:</td>
                                    <td>{{ $selectedSale->{'23_senarai_jualan_id'} }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Product:</td>
                                    <td>{{ $selectedSale->variety_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">SKU:</td>
                                    <td>{{ $selectedSale->sku_number }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Category:</td>
                                    <td>{{ $selectedSale->product_category_code }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Purity:</td>
                                    <td><span class="badge {{ $selectedSale->purity == '999' ? 'bg-warning' : 'bg-info' }}">{{ $selectedSale->purity }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Price:</td>
                                    <td class="fw-bold text-success">RM {{ number_format($selectedSale->total_price, 2) }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3 text-muted">Agent & Organization</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-medium">Agent:</td>
                                    <td>{{ $selectedSale->user_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Email:</td>
                                    <td>{{ $selectedSale->user_email }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Role:</td>
                                    <td><span class="badge bg-primary">{{ $selectedSale->role_name ?? 'N/A' }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Cooperative:</td>
                                    <td>{{ $selectedSale->cooperative_name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Branch:</td>
                                    <td>{{ $selectedSale->branch_name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Sale Date:</td>
                                    <td>{{ \Carbon\Carbon::parse($selectedSale->created_at)->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif
</div>
