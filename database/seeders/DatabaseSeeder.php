<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed in the correct order due to foreign key dependencies
        $this->call([
            // 1. Roles first (no dependencies)
            AcsRoleSeeder::class,

            // 2. Cooperatives (no dependencies)
            AcsCooperativeSeeder::class,

            // 3. Cooperative branches (depends on cooperatives)
            AcsCooperativeBranchSeeder::class,

            // 4. Income ranges (no dependencies)
            AcsRangeIncomeSeeder::class,

            // 5. Bank names (no dependencies)
            AcsBankNamesSeeder::class,

            // 6. Admin users (depends on roles)
            SuperAdminOnlySeeder::class,
            AdminOnlySeeder::class,

            // 7. Main agents (depends on roles, cooperatives, branches)
            MainAgentOnlySeeder::class,

            // 8. Agents (depends on roles, cooperatives, branches, main agents for invitation links)
            AgentOnlySeeder::class,

            // 9. User details for main agents and agents only (depends on users and income ranges)
            AcsUsersDetailsSeeder::class,

            // 10. Referral code settings (no dependencies)
            // AcsReferralCodeSettingSeeder::class,

            // 11. Generate referral codes for users
            // ReferralCodeSeeder::class,

            AcsWorkingPositionsSeeder::class,

            // 12. Other seeders
            //CampaignSeeder::class,
            //CommissionSettingsSeeder::class,

            // 13. Sales commission data (depends on users and campaigns)
            //SalesCommissionSeeder::class,

            // 14. Announcements (depends on roles)
            AcsAnnouncementSeeder::class,

            // 15. Sales data (depends on users)
            AcsSalesSeeder::class,
        ]);

        $this->command->info('🎉 All seeders completed successfully!');
        $this->command->info('');
        $this->command->info('📋 Summary of created data:');
        $this->command->info('• Roles: super-admin, admin, main-agent, agent');
        $this->command->info('• Cooperatives: 8 Malaysian cooperatives with realistic names');
        $this->command->info('• Branches: Multiple branches for each cooperative');
        $this->command->info('• Income Ranges: 9 income brackets (RM0 - RM999,999)');
        $this->command->info('• Bank Names: 20 Malaysian banks');
        $this->command->info('• Super Admin: 1 user (<EMAIL>)');
        $this->command->info('• Admin: 1 user (<EMAIL>)');
        $this->command->info('• Main Agent: 1 user (<EMAIL>)');
        $this->command->info('• Agent: 1 user (<EMAIL>)');
        $this->command->info('• User Details: Complete KYC details for all main-agents and agents');
        $this->command->info('• Referral Code Settings: 3 referral code generation methods');
        $this->command->info('• Referral Codes: Generated for all users');
        $this->command->info('• Announcements: Sample announcements for all roles');
        $this->command->info('• Sales Data: Sample sales records with purity 916 and 999 for all agents');
        $this->command->info('');
        $this->command->info('🔑 Default password for all users: password123');
        $this->command->info('📧 Test user emails: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>');
        $this->command->info('📋 User details include IC numbers, addresses, bank accounts, and KYC status');

        // Uncomment if you want to create factory users
        // User::factory(10)->create();
    }
}
