<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\AcsSales;
use App\Models\AcsUser;

class AcsSalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get agent role ID
        $agentRole = DB::table('acs_roles')
            ->where('name', 'agent')
            ->first();

        if (!$agentRole) {
            $this->command->error('Agent role not found. Please run AcsRoleSeeder first.');
            return;
        }

        // Get all agent users
        $agentUsers = AcsUser::where('acs_role_id', $agentRole->id)
            ->where('status', 'active')
            ->get();

        if ($agentUsers->isEmpty()) {
            $this->command->error('No active agent users found. Please run AgentOnlySeeder first.');
            return;
        }

        $this->command->info("Found {$agentUsers->count()} agent users for sales data.");

        // Sample sales data with purity 916 and 999
        $salesData = [
            [
                'purity' => '916',
                'variety_type' => 'Gold Ring',
                'product_category_code' => 'GR001',
                'sku_number' => 'SKU-GR916-001',
                'total_price' => 2500.00,
            ],
            [
                'purity' => '999',
                'variety_type' => 'Gold Necklace',
                'product_category_code' => 'GN001',
                'sku_number' => 'SKU-GN999-001',
                'total_price' => 5500.00,
            ],
            [
                'purity' => '916',
                'variety_type' => 'Gold Bracelet',
                'product_category_code' => 'GB001',
                'sku_number' => 'SKU-GB916-001',
                'total_price' => 1800.00,
            ],
            [
                'purity' => '999',
                'variety_type' => 'Gold Earrings',
                'product_category_code' => 'GE001',
                'sku_number' => 'SKU-GE999-001',
                'total_price' => 3200.00,
            ],
            [
                'purity' => '916',
                'variety_type' => 'Gold Chain',
                'product_category_code' => 'GC001',
                'sku_number' => 'SKU-GC916-001',
                'total_price' => 4100.00,
            ],
            [
                'purity' => '999',
                'variety_type' => 'Gold Pendant',
                'product_category_code' => 'GP001',
                'sku_number' => 'SKU-GP999-001',
                'total_price' => 2800.00,
            ],
        ];

        $createdSales = [];
        $saleId = 1;

        // Create sales records for each agent
        foreach ($agentUsers as $agent) {
            foreach ($salesData as $index => $saleData) {
                $sale = AcsSales::create([
                    '23_senarai_jualan_id' => $saleId,
                    'acs_users_id' => $agent->id,
                    'purity' => $saleData['purity'],
                    'sku_number' => $saleData['sku_number'],
                    'total_price' => $saleData['total_price'],
                    'variety_type' => $saleData['variety_type'],
                    'product_category_code' => $saleData['product_category_code'],
                ]);

                $createdSales[] = $sale;
                $saleId++;
            }
        }

        $this->command->info("✅ Created " . count($createdSales) . " sales records");
        $this->command->info("📊 Sales data includes:");
        $this->command->info("   • Purity levels: 916 and 999");
        $this->command->info("   • Product types: Rings, Necklaces, Bracelets, Earrings, Chains, Pendants");
        $this->command->info("   • Price range: RM 1,800 - RM 5,500");
        $this->command->info("   • Assigned to all active agent users");
        
        // Display sample data
        $this->command->info("\n📋 Sample sales records:");
        foreach ($salesData as $index => $data) {
            $this->command->info("   {$data['variety_type']} (Purity: {$data['purity']}) - RM " . number_format($data['total_price'], 2));
        }
    }
}
