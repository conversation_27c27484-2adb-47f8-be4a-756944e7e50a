<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_sales', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->integer('23_senarai_jualan_id');
            $table->foreignUlid('acs_users_id')->constrained('acs_users')->onDelete('cascade');
            $table->string('purity');
            $table->string('sku_number');
            $table->decimal('total_price', 15, 2);
            $table->string('variety_type');
            $table->string('product_category_code');
            $table->timestamps();
            $table->softDeletes();
            
            // Add indexes for better performance
            $table->index('23_senarai_jualan_id');
            $table->index('acs_users_id');
            $table->index('purity');
            $table->index('product_category_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_sales');
    }
};
