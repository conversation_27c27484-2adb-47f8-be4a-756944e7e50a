<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AcsSales extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    protected $table = 'acs_sales';

    protected $fillable = [
        '23_senarai_jualan_id',
        'acs_users_id',
        'purity',
        'sku_number',
        'total_price',
        'variety_type',
        'product_category_code',
    ];

    protected $casts = [
        '23_senarai_jualan_id' => 'integer',
        'total_price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns this sale
     */
    public function user()
    {
        return $this->belongsTo(AcsUser::class, 'acs_users_id');
    }
}
